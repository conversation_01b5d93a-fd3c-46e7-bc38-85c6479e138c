<?php

namespace Database\Factories;

use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectFactory extends Factory
{
    protected $model = Project::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' Token',
            'symbol' => strtoupper($this->faker->lexify('???')),
            'contract_address' => '0x' . $this->faker->regexify('[a-fA-F0-9]{40}'),
            'chain_id' => $this->faker->randomElement(['1', '56', '42161', '137', '324', '59144', '8453', '534352', '10', '43114', '250', '25', '66', '128', '100', '10001', 'tron', '321', '201022', '5000', '204', '42766', '81457', '169', '80094', '2741', '177', '146', '1514']),
            'whitepaper_type' => $this->faker->randomElement(['pdf', 'url']),
            'whitepaper_path' => $this->faker->optional()->filePath(),
            'whitepaper_url' => $this->faker->optional()->url(),
            'total_supply' => $this->faker->randomFloat(2, 1000000, *********0000),
            'market_cap' => $this->faker->optional()->randomFloat(2, 100000, *********),
            'predicted_price' => $this->faker->optional()->randomFloat(8, 0.000001, 100),
            'overall_score' => $this->faker->optional()->numberBetween(10, 100),
            'status' => $this->faker->randomElement(['pending', 'analyzing', 'completed', 'failed']),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => now(),
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'overall_score' => $this->faker->numberBetween(50, 100),
        ]);
    }

    public function analyzing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'analyzing',
            'overall_score' => null,
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'overall_score' => null,
            'error_message' => 'Analysis failed due to API error',
        ]);
    }

    public function highScore(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'overall_score' => $this->faker->numberBetween(80, 100),
        ]);
    }

    public function lowScore(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'overall_score' => $this->faker->numberBetween(10, 40),
        ]);
    }
}
