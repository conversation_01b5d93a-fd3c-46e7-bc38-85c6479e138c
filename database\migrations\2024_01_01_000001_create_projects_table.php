<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('symbol')->nullable();
            $table->string('contract_address');
            $table->string('chain_id');
            $table->string('whitepaper_type')->default('pdf'); // 'pdf' or 'url'
            $table->text('whitepaper_path')->nullable(); // file path for PDF
            $table->text('whitepaper_url')->nullable(); // URL for online whitepaper
            $table->longText('whitepaper_content')->nullable(); // extracted text content
            $table->decimal('total_supply', 20, 2)->nullable();
            $table->decimal('market_cap', 20, 2)->nullable();
            $table->decimal('predicted_price', 20, 8)->nullable();
            $table->integer('overall_score')->nullable(); // 1-100
            $table->json('security_analysis')->nullable(); // GoPlus API results
            $table->json('whitepaper_analysis')->nullable(); // OpenAI analysis results
            $table->json('tokenomics_analysis')->nullable(); // Tokenomics breakdown
            $table->enum('status', ['pending', 'analyzing', 'completed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->timestamps();
            
            $table->index(['contract_address', 'chain_id']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
