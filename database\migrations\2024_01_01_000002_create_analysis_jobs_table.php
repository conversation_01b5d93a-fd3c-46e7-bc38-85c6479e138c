<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('analysis_jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('job_type'); // 'security_scan', 'whitepaper_analysis', 'price_prediction'
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->json('job_data')->nullable(); // Input data for the job
            $table->json('result_data')->nullable(); // Output data from the job
            $table->text('error_message')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->index(['project_id', 'job_type']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('analysis_jobs');
    }
};
