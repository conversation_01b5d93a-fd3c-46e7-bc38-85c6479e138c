<?php

namespace App\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoPlusService
{
    private string $apiKey;
    private string $baseUrl = 'https://api.gopluslabs.io/api/v1';

    public function __construct()
    {
        $this->apiKey = config('services.goplus.api_key') ?? env('GOPLUS_API_KEY');
        
        if (!$this->apiKey) {
            throw new \Exception('GoPlus API key is not configured');
        }
    }

    /**
     * Analyze token security using GoPlus API.
     */
    public function analyzeTokenSecurity(string $contractAddress, string $chainId): array
    {
        try {
            $response = $this->makeRequest('token_security/' . $chainId, [
                'contract_addresses' => $contractAddress
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['result'][$contractAddress])) {
                    $tokenData = $data['result'][$contractAddress];
                    return $this->processSecurityData($tokenData);
                }
                
                throw new \Exception('Token data not found in GoPlus response');
            }

            throw new \Exception('GoPlus API request failed: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('GoPlus security analysis failed', [
                'error' => $e->getMessage(),
                'contract_address' => $contractAddress,
                'chain_id' => $chainId
            ]);
            
            throw $e;
        }
    }

    /**
     * Get token information using GoPlus API.
     */
    public function getTokenInfo(string $contractAddress, string $chainId): array
    {
        try {
            $response = $this->makeRequest('token_security/' . $chainId, [
                'contract_addresses' => $contractAddress
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['result'][$contractAddress])) {
                    $tokenData = $data['result'][$contractAddress];
                    return $this->extractTokenInfo($tokenData);
                }
                
                throw new \Exception('Token data not found in GoPlus response');
            }

            throw new \Exception('GoPlus API request failed: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('GoPlus token info retrieval failed', [
                'error' => $e->getMessage(),
                'contract_address' => $contractAddress,
                'chain_id' => $chainId
            ]);
            
            throw $e;
        }
    }

    /**
     * Process security data from GoPlus response.
     */
    private function processSecurityData(array $tokenData): array
    {
        $securityScore = $this->calculateSecurityScore($tokenData);
        
        return [
            'score' => $securityScore,
            'is_open_source' => $this->getBooleanValue($tokenData, 'is_open_source'),
            'is_proxy' => $this->getBooleanValue($tokenData, 'is_proxy'),
            'is_mintable' => $this->getBooleanValue($tokenData, 'is_mintable'),
            'can_take_back_ownership' => $this->getBooleanValue($tokenData, 'can_take_back_ownership'),
            'owner_change_balance' => $this->getBooleanValue($tokenData, 'owner_change_balance'),
            'hidden_owner' => $this->getBooleanValue($tokenData, 'hidden_owner'),
            'selfdestruct' => $this->getBooleanValue($tokenData, 'selfdestruct'),
            'external_call' => $this->getBooleanValue($tokenData, 'external_call'),
            'buy_tax' => $this->getStringValue($tokenData, 'buy_tax'),
            'sell_tax' => $this->getStringValue($tokenData, 'sell_tax'),
            'is_honeypot' => $this->getBooleanValue($tokenData, 'is_honeypot'),
            'transfer_pausable' => $this->getBooleanValue($tokenData, 'transfer_pausable'),
            'is_blacklisted' => $this->getBooleanValue($tokenData, 'is_blacklisted'),
            'is_whitelisted' => $this->getBooleanValue($tokenData, 'is_whitelisted'),
            'is_in_dex' => $this->getBooleanValue($tokenData, 'is_in_dex'),
            'trading_cooldown' => $this->getBooleanValue($tokenData, 'trading_cooldown'),
            'personal_slippage_modifiable' => $this->getBooleanValue($tokenData, 'personal_slippage_modifiable'),
            'holder_count' => $this->getStringValue($tokenData, 'holder_count'),
            'total_supply' => $this->getStringValue($tokenData, 'total_supply'),
            'creator_address' => $this->getStringValue($tokenData, 'creator_address'),
            'creator_balance' => $this->getStringValue($tokenData, 'creator_balance'),
            'creator_percent' => $this->getStringValue($tokenData, 'creator_percent'),
            'lp_holder_count' => $this->getStringValue($tokenData, 'lp_holder_count'),
            'lp_total_supply' => $this->getStringValue($tokenData, 'lp_total_supply'),
            'risk_factors' => $this->identifyRiskFactors($tokenData),
            'security_warnings' => $this->generateSecurityWarnings($tokenData),
            'raw_data' => $tokenData
        ];
    }

    /**
     * Extract basic token information.
     */
    private function extractTokenInfo(array $tokenData): array
    {
        return [
            'token_name' => $this->getStringValue($tokenData, 'token_name'),
            'token_symbol' => $this->getStringValue($tokenData, 'token_symbol'),
            'total_supply' => $this->getStringValue($tokenData, 'total_supply'),
            'holder_count' => $this->getStringValue($tokenData, 'holder_count'),
            'creator_address' => $this->getStringValue($tokenData, 'creator_address'),
            'creator_balance' => $this->getStringValue($tokenData, 'creator_balance'),
            'creator_percent' => $this->getStringValue($tokenData, 'creator_percent'),
            'is_in_dex' => $this->getBooleanValue($tokenData, 'is_in_dex'),
        ];
    }

    /**
     * Calculate security score based on various factors.
     */
    private function calculateSecurityScore(array $tokenData): int
    {
        $score = 100;
        
        // Major red flags (high impact)
        if ($this->getBooleanValue($tokenData, 'is_honeypot')) $score -= 50;
        if ($this->getBooleanValue($tokenData, 'selfdestruct')) $score -= 30;
        if ($this->getBooleanValue($tokenData, 'hidden_owner')) $score -= 25;
        if ($this->getBooleanValue($tokenData, 'can_take_back_ownership')) $score -= 20;
        if ($this->getBooleanValue($tokenData, 'owner_change_balance')) $score -= 20;
        
        // Medium impact issues
        if ($this->getBooleanValue($tokenData, 'is_mintable')) $score -= 15;
        if ($this->getBooleanValue($tokenData, 'transfer_pausable')) $score -= 15;
        if ($this->getBooleanValue($tokenData, 'is_proxy')) $score -= 10;
        if ($this->getBooleanValue($tokenData, 'external_call')) $score -= 10;
        
        // Tax analysis
        $buyTax = (float) $this->getStringValue($tokenData, 'buy_tax');
        $sellTax = (float) $this->getStringValue($tokenData, 'sell_tax');
        
        if ($buyTax > 0.1) $score -= 10; // >10% buy tax
        if ($sellTax > 0.1) $score -= 10; // >10% sell tax
        if ($buyTax > 0.2 || $sellTax > 0.2) $score -= 20; // >20% tax
        
        // Positive factors
        if ($this->getBooleanValue($tokenData, 'is_open_source')) $score += 5;
        if (!$this->getBooleanValue($tokenData, 'is_mintable')) $score += 5;
        
        return max(0, min(100, $score));
    }

    /**
     * Identify risk factors from token data.
     */
    private function identifyRiskFactors(array $tokenData): array
    {
        $risks = [];
        
        if ($this->getBooleanValue($tokenData, 'is_honeypot')) {
            $risks[] = 'Honeypot detected - tokens may not be sellable';
        }
        
        if ($this->getBooleanValue($tokenData, 'selfdestruct')) {
            $risks[] = 'Contract can be self-destructed by owner';
        }
        
        if ($this->getBooleanValue($tokenData, 'hidden_owner')) {
            $risks[] = 'Contract owner is hidden or obfuscated';
        }
        
        if ($this->getBooleanValue($tokenData, 'can_take_back_ownership')) {
            $risks[] = 'Ownership can be reclaimed by previous owner';
        }
        
        if ($this->getBooleanValue($tokenData, 'owner_change_balance')) {
            $risks[] = 'Owner can modify user balances';
        }
        
        if ($this->getBooleanValue($tokenData, 'is_mintable')) {
            $risks[] = 'Additional tokens can be minted';
        }
        
        if ($this->getBooleanValue($tokenData, 'transfer_pausable')) {
            $risks[] = 'Token transfers can be paused';
        }
        
        $buyTax = (float) $this->getStringValue($tokenData, 'buy_tax');
        $sellTax = (float) $this->getStringValue($tokenData, 'sell_tax');
        
        if ($buyTax > 0.1) {
            $risks[] = "High buy tax: " . ($buyTax * 100) . "%";
        }
        
        if ($sellTax > 0.1) {
            $risks[] = "High sell tax: " . ($sellTax * 100) . "%";
        }
        
        return $risks;
    }

    /**
     * Generate security warnings.
     */
    private function generateSecurityWarnings(array $tokenData): array
    {
        $warnings = [];
        
        if (!$this->getBooleanValue($tokenData, 'is_open_source')) {
            $warnings[] = 'Contract source code is not verified';
        }
        
        if ($this->getBooleanValue($tokenData, 'is_proxy')) {
            $warnings[] = 'Contract uses proxy pattern - implementation can be changed';
        }
        
        if ($this->getBooleanValue($tokenData, 'external_call')) {
            $warnings[] = 'Contract makes external calls - potential reentrancy risk';
        }
        
        $creatorPercent = (float) $this->getStringValue($tokenData, 'creator_percent');
        if ($creatorPercent > 0.5) {
            $warnings[] = "Creator holds {$creatorPercent}% of total supply";
        }
        
        return $warnings;
    }

    /**
     * Make HTTP request to GoPlus API.
     */
    private function makeRequest(string $endpoint, array $params = []): Response
    {
        $url = "{$this->baseUrl}/{$endpoint}";
        
        return Http::withHeaders([
            'API-KEY' => $this->apiKey
        ])->timeout(30)->get($url, $params);
    }

    /**
     * Get boolean value from token data.
     */
    private function getBooleanValue(array $data, string $key): bool
    {
        $value = $data[$key] ?? '0';
        return $value === '1' || $value === 1 || $value === true;
    }

    /**
     * Get string value from token data.
     */
    private function getStringValue(array $data, string $key): string
    {
        return (string) ($data[$key] ?? '');
    }
}
